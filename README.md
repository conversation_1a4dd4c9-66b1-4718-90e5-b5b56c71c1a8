# 保赢H5项目 - 保险平台开发指南

这是一个基于React + UmiJS的移动端H5保险平台项目，主要服务于"阿保保险"/"澎湃保"的保险销售和管理业务。

## 📋 项目概述

**项目名称**: 保赢 H5 (BaoYing H5)
**目标平台**: 移动端Web (H5)
**主要用户**: 保险代理人和客户
**业务领域**: 保险销售和管理平台

## 🚀 快速开始

```bash
# 安装依赖
npm install
# 或
yarn install

# 启动开发服务器
npm start
# 或
yarn start

# 构建生产版本
npm run build
# 或
yarn build

# 代码检查
npm run lint
# 或
yarn lint

# 运行测试
npm test
# 或
yarn test
```

## 🏗️ 技术架构

- **前端框架**: React 16.7+ + UmiJS 2.7
- **状态管理**: Dva (基于Redux)
- **UI组件库**: antd-mobile (v2.2.13 + v5)
- **路由方式**: Hash路由 + 动态导入
- **构建工具**: Umi内置webpack配置 + 自定义插件
- **样式方案**: SCSS模块化 + CSS-in-JS支持
- **API通信**: 自定义request工具 + AES加密 + token刷新
- **移动端优化**: 响应式设计 + rem单位 + viewport配置

## 📁 项目结构

```text
src/
├── pages/              # 主要页面组件（按业务模块组织）
│   ├── ProductGPIC/    # 太平洋保险产品模块
│   ├── ProductZKI/     # 紫金保险产品模块
│   ├── ProductMY/      # 美亚保险产品模块
│   ├── ProductMul/     # 多保险产品模块
│   ├── ProductNew/     # 新保险产品模块
│   ├── ProductTeam/    # 团体保险产品模块
│   ├── My/             # 个人中心
│   ├── Customer/       # 客户管理
│   ├── Order/          # 订单管理
│   ├── Policy/         # 保单查询
│   └── ...
├── models/             # Dva状态管理模型
│   ├── common.js       # 公共状态
│   ├── login.js        # 登录状态
│   ├── customer.js     # 客户管理状态
│   ├── order.js        # 订单状态
│   └── ...
├── services/           # API服务层
│   ├── product.js      # 产品相关API
│   ├── common.js       # 公共API
│   ├── customer.js     # 客户管理API
│   └── ...
├── components/         # 可复用UI组件和业务组件
├── utils/              # 工具函数
│   ├── request.js      # API请求工具
│   ├── CryptoUtil.js   # 加密工具
│   ├── utils.js        # 通用工具
│   └── ...
├── assets/             # 静态资源
└── config/             # 配置文件
```

## 🔧 核心业务模块

### 保险产品模块
- **ProductGPIC**: 太平洋保险(GPIC)产品
- **ProductZKI**: 紫金保险产品
- **ProductMY**: 美亚保险产品
- **ProductMul**: 多保险产品
- **ProductNew**: 新保险产品
- **ProductTeam**: 团体保险产品

### 用户管理模块
- **My**: 个人中心 - 信息管理、团队管理、业绩跟踪
- **Customer**: 客户管理 - 代理人客户管理
- **Policy**: 保单查询和管理
- **Order**: 订单管理和跟踪

### 业务流程模块
- **Active**: 营销活动和定制保险计划
- **Insurance**: 保险百科和赠险
- **Correct**: 保单更正工作流
- **Authorize**: 授权和认证流程

### 公共页面模块
- **PublicLogin**: 无微信集成的登录
- **PublicHome**: 公共首页
- **PublicAddress**: 城市选择
- **PublicWeather**: 天气信息

## 🔧 现有保险公司产品模块结构

每个保险公司产品模块都遵循统一的结构模式：

```text
ProductXXX/                    # 保险公司产品模块
├── index.js                   # 产品详情主页面
├── Footer.js                  # 底部操作栏
├── ProHeader.js               # 产品头部信息
├── ProContent.js              # 产品内容展示
├── index.scss                 # 样式文件
├── Insured/                   # 投保相关页面
│   ├── index.js               # 投保主页面
│   └── confirm.js             # 投保确认页面
├── Pay/                       # 支付相关页面
│   └── index.js               # 支付页面
├── models/                    # 状态管理
│   ├── product.js             # 产品相关状态
│   ├── health.js              # 健康告知状态
│   └── insuredNew.js          # 投保状态
├── assets/                    # 资源文件
│   └── productConfig/         # 产品配置系统
│       ├── Products.js        # 产品配置类
│       ├── judgeProductFeature.js # 产品特性判断
│       └── index.md           # 配置说明文档
├── components/                # 业务组件
│   └── form/                  # 表单组件
└── images/                    # 图片资源
```

## 🎯 产品配置系统

项目采用统一的产品配置系统，支持不同保险公司的差异化处理：

### 配置文件结构
- `assets/productConfig/Products.js`: 产品配置类
- `assets/productConfig/judgeProductFeature.js`: 产品特性判断和导出
- `assets/productConfig/index.md`: 配置使用说明

### 配置示例
```javascript
// 添加新产品配置
export const NEW_PRODUCT = products.push(
  ['产品名称'],           // 支持数组形式的多个名称
  'PRODUCT_CODE',        // 自定义产品代码
  {
    fixedPrice: '首月1元',              // 产品详情固定价格展示
    fixedPayPrice: '支付固定价格',       // 支付固定价格展示
    isHasAbaoService: true,             // 是否展示阿保客服
    premiumTrialTitle: '保费试算',       // 保费试算弹窗标题
    urlAddFlag: 'productapi',           // API路径标识
    insuranceType: 1,                   // 保险类型 (1: 非车险)
    insuredAgeboundaryDayDiff: {        // 投保年龄边界天数差值
      max: -3,
      min: -4,
    },
    insuranceTips: {                    // 可回溯弹窗配置
      title: '投保流程提示',
      content: '投保提示内容...',
      submitText: '确定'
    }
  }
);
```

## 🚀 新保险公司对接完整开发流程

### 第一阶段：需求分析与准备 (1-2天)

#### 1.1 业务需求梳理
- **保险产品类型分析**: 确定是团险、个险还是其他类型
- **投保流程差异**: 了解该保险公司特有的投保流程要求
- **API接口文档**: 获取保险公司提供的API接口文档
- **UI设计要求**: 确认是否有特殊的UI展示要求

#### 1.2 技术对接准备
- **API端点配置**: 确定新保险公司的API基础路径
- **认证方式**: 了解API认证机制（token、签名等）
- **数据格式**: 确认请求/响应数据格式差异
- **特殊业务逻辑**: 识别与现有保险公司的差异点

### 第二阶段：产品模块创建 (2-3天)

#### 2.1 创建产品模块目录
```bash
# 假设新保险公司代码为 ABC
src/pages/ProductABC/
```

#### 2.2 复制基础模板
**推荐基于现有模块复制**:
- 如果是团险产品，建议基于 `ProductGPIC` 复制
- 如果是个险产品，建议基于 `ProductMY` 复制

**需要创建的核心文件**:
```text
ProductABC/
├── index.js              # 主页面组件
├── Footer.js             # 底部操作栏
├── ProHeader.js          # 产品头部
├── ProContent.js         # 产品内容
├── index.scss            # 样式文件
├── models/
│   └── product.js        # 产品状态管理
├── assets/
│   └── productConfig/    # 产品配置
│       ├── Products.js
│       └── judgeProductFeature.js
└── Insured/              # 投保流程页面
    ├── index.js          # 投保主页面
    └── confirm.js        # 投保确认页面
```

#### 2.3 配置产品特性
**修改产品配置文件**:
```javascript
// 新增ABC保险产品配置
export const ABC_PRODUCT = products.push(
  ['ABC保险产品名称'],
  'ABC_CODE',
  {
    insuredAgeboundaryDayDiff: {
      max: -3,
      min: -4,
    },
    urlAddFlag: 'abc',  // API路径标识
    fixedPrice: '首月1元',
    isHasAbaoService: true,
    insuranceType: 1, // 非车险
  }
);
```

### 第三阶段：API服务层开发 (2-3天)

#### 3.1 扩展产品服务
**修改 `src/services/product.js`**:
```javascript
// 新增ABC保险公司特有的API接口
export async function queryABCProductDetail(params) {
  let currProd = getSpecialProduct();
  return request(`/baoying-product-center/api/${currProd?.urlAddFlag || 'abc'}/product/detail`, {
    method: 'POST',
    body: params,
  });
}

// ABC保险公司投保接口
export async function submitABCInsurance(params) {
  const token = getCustomerToken();
  let currProd = getSpecialProduct();
  return request(`/baoying-product-center/api/${currProd?.urlAddFlag || 'abc'}/submit?blade-auth=${token}`, {
    method: 'POST',
    body: params,
  });
}
```

#### 3.2 配置API代理
**修改 `config/config.js`** 中的proxy配置:
```javascript
proxy: {
  '/baoying': {
    target: 'https://ppb.dev2.abaobaoxian.com',
    changeOrigin: true,
  },
  // 如果ABC保险公司需要特殊的API端点
  '/abc-api': {
    target: 'https://abc-insurance-api.com',
    changeOrigin: true,
  }
}
```

### 第四阶段：页面组件开发 (3-4天)

#### 4.1 产品详情页面开发
**主要文件**: `src/pages/ProductABC/index.js`

**关键开发点**:
- 复用现有的页面结构和组件
- 根据ABC保险公司要求调整UI展示
- 集成产品配置系统
- 处理特殊的业务逻辑

#### 4.2 投保流程页面开发
**主要文件**: `src/pages/ProductABC/Insured/index.js`

**关键开发点**:
- 投保表单字段适配
- 健康告知流程定制
- 投保确认页面调整
- 支付流程集成

#### 4.3 状态管理开发
**主要文件**: `src/pages/ProductABC/models/product.js`
```javascript
export default {
  namespace: 'productABC',
  state: {
    queryProduct: null,
    quoteInfo: null,
  },
  effects: {
    * queryProductInfo({ payload }, { call, put }) {
      const response = yield call(requestMethod.queryABCProductDetail, payload);
      if (response && response.code === 0) {
        yield put({
          type: 'GetProductDetail',
          payload: response.payload,
        });
        return response;
      }
    },
  },
  reducers: {
    GetProductDetail(state, action) {
      return {
        ...state,
        queryProduct: action.payload
      };
    },
  }
};
```

### 第五阶段：路由配置与集成 (1天)

#### 5.1 路由自动生成
UmiJS会根据文件结构自动生成路由，新建的 `ProductABC` 模块会自动可访问。

#### 5.2 导航集成
**修改相关导航页面**，添加新保险公司产品的入口:
- `src/pages/Home/index.js` - 首页产品列表
- `src/pages/PublicHome/index.js` - 公共首页

### 第六阶段：测试与优化 (2-3天)

#### 6.1 功能测试
- **产品详情展示测试**
- **投保流程完整性测试**
- **支付流程测试**
- **异常情况处理测试**

#### 6.2 兼容性测试
- **移动端适配测试**
- **微信环境测试**
- **不同浏览器兼容性测试**

#### 6.3 性能优化
- **代码分割优化**
- **图片资源优化**
- **API请求优化**

## ⚠️ 关键注意事项

### 🔒 安全考虑
- **API加密**: 确保敏感数据使用AES加密传输
- **Token管理**: 正确处理认证token的刷新机制
- **数据验证**: 前端表单验证 + 后端数据校验

### 🔄 代码复用性
- **组件复用**: 最大化复用现有UI组件
- **工具函数复用**: 利用 `src/utils/` 下的工具函数
- **样式复用**: 遵循现有的SCSS模块化规范

### 📱 移动端适配
- **响应式设计**: 确保在不同屏幕尺寸下正常显示
- **触摸优化**: 优化移动端交互体验
- **性能考虑**: 控制页面加载时间和资源大小

### 🚨 风险点识别
1. **API接口差异**: 不同保险公司API格式可能存在较大差异
2. **业务流程差异**: 投保流程、健康告知等可能有特殊要求
3. **数据格式兼容**: 确保数据格式与后端服务兼容
4. **第三方依赖**: 可能需要集成保险公司特有的SDK或组件

## 📊 开发时间估算

| 阶段 | 预估时间 | 主要工作内容 |
|------|----------|--------------|
| 需求分析与准备 | 1-2天 | 业务需求梳理、技术对接准备 |
| 产品模块创建 | 2-3天 | 目录结构创建、基础模板复制 |
| API服务层开发 | 2-3天 | 接口开发、代理配置 |
| 页面组件开发 | 3-4天 | 页面开发、状态管理 |
| 路由配置与集成 | 1天 | 路由配置、导航集成 |
| 测试与优化 | 2-3天 | 功能测试、性能优化 |
| **总计** | **11-16天** | **完整开发周期** |

## 🔧 环境配置

### 开发环境
- **代理目标**: `https://ppb.dev2.abaobaoxian.com`
- **API端点**: `/baoying` 和 `/blade` 前缀
- **微信集成**: JSSDK 1.6.0 用于分享和位置服务
- **高德地图集成**: API密钥 `7c92b6b24fffc3137939da5908e3cdf3`
- **客户端凭证**:
  - Client ID: `ppb`
  - Client Secret: `ppb#2021`
- **加密密钥**: `cheche365#2024$pengpaibaoencrypt` (用于AES加密)

### 移动端适配
- **视口配置**: 针对移动设备优化的响应式设计
- **Rem单位**: 基于屏幕宽度的动态字体大小计算 (clientWidth / 7.5)
- **屏幕约束**:
  - 最大宽度: 750px
  - 最小宽度: 320px
- **方向处理**: 自动适应横屏/竖屏模式
- **触摸优化**: better-scroll库提供流畅滚动体验

## 📝 开发规范

### 添加新页面
1. 在 `src/pages/` 下创建新目录
2. 添加 `index.js` 组件，包含标题注释: `/** title: 页面标题 */`
3. 如需要，在 `src/models/` 中添加对应模型
4. 如需要，在 `src/services/` 中添加服务方法
5. 遵循现有的样式和组件结构模式

### 状态管理模式
- 模型遵循模式: `namespace`, `state`, `effects` (异步操作), `reducers` (同步状态更新)
- Effects使用生成器函数，`yield call()` 用于API请求，`yield put()` 用于分发reducers
- 常用模型: `common`, `login`, `customer`, `order`, `health`

### API集成
- 所有API调用通过 `src/utils/request.js`
- 请求工具包含:
  - 自动token刷新机制
  - AES加密/解密，密钥: `cheche365#2024$pengpaibaoencrypt`
  - 用户友好的错误处理
  - 加载状态管理
- `src/services/` 中的服务导入并使用请求工具
- 使用localStorage的token管理和刷新token流程

### 样式指南
- 使用SCSS模块，文件扩展名为 `.scss`
- 导入样式: `import styles from './index.scss'`
- 应用类名: `className={styles.className}`
- 遵循移动优先的响应式设计原则
- 使用rem单位确保可扩展性

### 微信集成
- 微信JSSDK集成用于分享和位置服务
- 微信登录的OAuth流程
- 不同环境的自定义用户代理处理
- 营销活动的分享功能

### 安全特性
- 敏感API通信的AES加密
- 基于token的认证和自动刷新
- 请求指纹的CSRF保护
- 用户凭证的安全存储

## 🧪 测试

使用 `yarn test` 运行测试。项目使用Umi内置的测试功能和React Test Renderer。

## 🏗️ 构建过程

- 使用Umi的默认配置和自定义webpack插件
- 动态导入的代码分割提升性能
- 生产构建包含:
  - 控制台日志移除 (保留错误和警告)
  - CSS优化和压缩
  - Moment.js本地化优化
  - 外部依赖 (微信SDK)
  - 更好缓存的vendor chunk分割
- 输出生成在 `dist/` 目录
- 基于Hash的路由提升SEO和缓存
- 移动端性能的bundle分析和优化

### 性能优化

- **代码分割**: 基于路由的动态导入代码分割
- **Vendor分块**: React、core-js、moment和大型库的独立chunks
- **Tree Shaking**: 生产构建启用以移除未使用代码
- **CSS优化**: SCSS编译，压缩和自动前缀
- **图片优化**: public目录中的优化资源
- **懒加载**: 按需加载组件和路由

## 🔍 调试技巧

- 检查浏览器控制台的API错误和网络问题
- 使用Redux DevTools进行状态管理调试
- 在微信浏览器环境中验证微信集成
- 测试认证流程的token刷新场景
- 在Chrome DevTools中使用移动设备模拟
- 检查网络请求的加密/解密问题
- 验证不同屏幕尺寸的rem单位计算

## 🚀 部署

### 开发环境
```bash
# 启动开发服务器
yarn start
# 访问 http://localhost:8000
```

### 生产构建
```bash
# 构建生产版本
yarn build
# 输出在 dist/ 目录
```

### 环境变量
- `NODE_ENV`: 控制开发/生产行为
- `APP_TYPE`: 应用类型配置

## 🔗 第三方集成

### 微信服务
- **JSSDK版本**: 1.6.0
- **功能**: 分享、位置、支付
- **配置**: 从后端动态获取配置

### 高德地图
- **版本**: 1.4.15
- **API密钥**: `7c92b6b24fffc3137939da5908e3cdf3`
- **功能**: 位置服务、地址选择

### 其他服务
- **HTML2Canvas**: 截图和图片生成
- **QRCode**: 分享的二维码生成
- **Better-scroll**: 增强的移动端滚动
- **Crypto-js**: 客户端加密工具

## 🛠️ 故障排除

### 常见问题

1. **微信集成**: 确保微信后端的域名配置正确
2. **Token过期**: 检查request.js中的自动刷新机制
3. **移动端兼容性**: 在真实设备上测试，不仅仅是模拟器
4. **性能**: 监控bundle大小和加载时间
5. **加密**: 验证前端和后端之间AES密钥的一致性

## 📚 常见开发模式

- 页面组件通常包括: 数据获取、表单处理、导航
- 使用 `router.push()` 进行带查询参数的导航
- 使用antd-mobile的 `ActivityIndicator` 实现加载状态
- 在各页面间一致地处理表单验证和提交
- 使用Toast通知进行用户反馈

---

这个开发流程基于现有项目的成熟架构，通过复用现有组件和模式，可以高效地完成新保险公司的产品对接。建议在开发过程中保持与产品经理和后端开发人员的密切沟通，确保需求理解准确和接口对接顺畅。
