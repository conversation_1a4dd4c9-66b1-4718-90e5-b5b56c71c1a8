# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview
This is a React-based mobile H5 frontend project for an insurance platform called "阿保保险" (AAB Insurance) / "澎湃保" (PengPaiBao), using the Umi framework. The project implements comprehensive insurance services including product displays, user management, policy management, order processing, and agent management functionalities.

**Project Name**: 保赢 H5 (BaoYing H5)
**Target Platform**: Mobile Web (H5)
**Primary Users**: Insurance agents and customers
**Business Domain**: Insurance sales and management platform

## Architecture
- **Framework**: React 16.7+ + UmiJS 2.7
- **State Management**: Dva (based on Redux)
- **UI Components**: antd-mobile (v2.2.13 + v5 for newer components)
- **Routing**: Hash-based routing with dynamic imports
- **Build Tool**: Umi's built-in webpack configuration with custom plugins
- **Styling**: Sass/SCSS modules with CSS-in-JS support
- **API Communication**: Custom request utility with AES encryption and token refresh
- **Mobile Optimization**: Responsive design with rem units and viewport configuration

## Key Directories
- `src/pages/`: Main page components organized by business modules
- `src/models/`: Dva models for state management (carOrder, customer, health, etc.)
- `src/services/`: API service layer with business-specific modules
- `src/components/`: Reusable UI components and business components
- `src/utils/`: Utility functions including request, crypto, and tools
- `config/`: Umi configuration files and webpack customization
- `public/`: Static assets and third-party resources
- `mock/`: Mock data for development

## Business Modules

### Core Insurance Products
- **ProductNew**: New insurance products with health statements
- **ProductMul**: Multi-insurance products 
- **ProductTeam**: Team/group insurance products
- **ProductGPIC**: GPIC (太平洋保险) specific products
- **ProductZKI**: ZKI insurance products
- **ProductMY**: MY insurance products

### User Management
- **My**: Personal center with info, team management, performance tracking
- **Customer**: Customer management for agents
- **Policy**: Policy inquiry and management
- **Order**: Order management and tracking

### Business Processes
- **Active**: Marketing activities and customized insurance plans
- **Insurance**: Insurance encyclopedia and gift insurance
- **Correct**: Policy correction workflows
- **Authorize**: Authorization and authentication flows

### Public Pages
- **PublicLogin**: Login without WeChat integration
- **PublicHome**: Public homepage
- **PublicAddress**: City selection
- **PublicWeather**: Weather information

## Common Development Tasks

### Running the Project
```bash
# Development
npm start
# or
yarn start

# Production Build
npm run build
# or
yarn build

# Testing
npm test
# or
yarn test

# Linting
npm run lint
# or
yarn lint
```

### Environment Configuration

- **Development proxy targets**: `https://ppb.dev2.abaobaoxian.com`
- **API endpoints**: `/baoying` and `/blade` prefixes
- **WeChat integration**: JSSDK 1.6.0 for sharing and location services
- **Amap integration**: Location services with API key `7c92b6b24fffc3137939da5908e3cdf3`
- **Client credentials**:
  - Client ID: `ppb`
  - Client Secret: `ppb#2021`
- **Encryption key**: `cheche365#2024$pengpaibaoencrypt` (for AES encryption)

### Mobile Adaptation

- **Viewport configuration**: Responsive design optimized for mobile devices
- **Rem units**: Dynamic font-size calculation based on screen width (clientWidth / 7.5)
- **Screen constraints**:
  - Maximum width: 750px
  - Minimum width: 320px
- **Orientation handling**: Automatic adjustment for portrait/landscape modes
- **Touch optimization**: Better-scroll library for smooth scrolling experience

### Adding a New Page
1. Create a new directory under `src/pages/`
2. Add `index.js` for the component with title comment: `/** title: 页面标题 */`
3. Add corresponding model if needed in `src/models/`
4. Add service methods in `src/services/` if required
5. Follow existing patterns for styling and component structure

### State Management Pattern
- Models follow the pattern: `namespace`, `state`, `effects` (async operations), `reducers` (sync state updates)
- Effects use generator functions with `yield call()` for API requests and `yield put()` for dispatching reducers
- Common models: `common`, `login`, `customer`, `order`, `health`

### API Integration
- All API calls go through `src/utils/request.js`
- Request utility includes:
  - Automatic token refresh mechanism
  - AES encryption/decryption with key: `cheche365#2024$pengpaibaoencrypt`
  - Error handling with user-friendly messages
  - Loading state management
- Services in `src/services/` import and use the request utility
- Token management with localStorage and refresh token flow

### Product Configuration System

Each product module has its own configuration system:

- `assets/productConfig/Products.js`: Product-specific configurations
- `assets/productConfig/judgeProductFeature.js`: Feature detection utilities
- Support for special pricing, custom services, and product-specific behaviors
- Dynamic product feature detection and customization
- Configurable insurance tips and customer notifications

### Styling Guidelines
- Use SCSS modules with `.scss` files
- Import styles as `import styles from './index.scss'`
- Apply classes with `className={styles.className}`
- Follow mobile-first responsive design principles
- Use rem units for scalability

### WeChat Integration
- WeChat JSSDK integration for sharing and location services
- OAuth flow for WeChat login
- Custom user agent handling for different environments
- Share functionality for marketing activities

### Security Features
- AES encryption for sensitive API communications
- Token-based authentication with automatic refresh
- CSRF protection with request fingerprinting
- Secure storage of user credentials

## Testing
Run tests with `yarn test`. The project uses Umi's built-in testing capabilities with React Test Renderer.

## Build Process

- Uses Umi's default configuration with custom webpack plugins
- Code splitting with dynamic imports for better performance
- Production builds include:
  - Console log removal (except errors and warnings)
  - CSS optimization and minification
  - Moment.js locale optimization
  - External dependencies (WeChat SDK)
  - Vendor chunk splitting for better caching
- Output is generated in the `dist/` directory
- Hash-based routing for better SEO and caching
- Bundle analysis and optimization for mobile performance

### Performance Optimizations

- **Code Splitting**: Dynamic imports for route-based code splitting
- **Vendor Chunking**: Separate chunks for React, core-js, moment, and large libraries
- **Tree Shaking**: Enabled for production builds to remove unused code
- **CSS Optimization**: SCSS compilation with minification and autoprefixing
- **Image Optimization**: Optimized assets in public directory
- **Lazy Loading**: Components and routes loaded on demand

## Development Best Practices
1. **Component Structure**: Follow the established pattern with title comments
2. **Error Handling**: Use consistent error messaging and loading states
3. **Mobile Optimization**: Test on various mobile devices and screen sizes
4. **Performance**: Leverage code splitting and lazy loading for large components
5. **Security**: Never expose sensitive keys or tokens in client-side code
6. **Accessibility**: Ensure proper mobile accessibility features

## Common Patterns
- Page components typically include: data fetching, form handling, navigation
- Use `router.push()` for navigation with query parameters
- Implement loading states with `ActivityIndicator` from antd-mobile
- Handle form validation and submission consistently across pages
- Use Toast notifications for user feedback

## Debugging Tips

- Check browser console for API errors and network issues
- Use Redux DevTools for state management debugging
- Verify WeChat integration in WeChat browser environment
- Test token refresh scenarios for authentication flows
- Use mobile device simulation in Chrome DevTools
- Check network requests for encryption/decryption issues
- Verify rem unit calculations on different screen sizes

## Deployment

### Development Environment

```bash
# Start development server
yarn start
# Access at http://localhost:8000
```

### Production Build

```bash
# Build for production
yarn build
# Output in dist/ directory
```

### Environment Variables

- `NODE_ENV`: Controls development/production behavior
- `APP_TYPE`: Application type configuration

## Third-Party Integrations

### WeChat Services

- **JSSDK Version**: 1.6.0
- **Features**: Sharing, location, payment
- **Configuration**: Dynamic config fetching from backend

### Amap (高德地图)

- **Version**: 1.4.15
- **API Key**: `7c92b6b24fffc3137939da5908e3cdf3`
- **Features**: Location services, address selection

### Other Services

- **HTML2Canvas**: Screenshot and image generation
- **QRCode**: QR code generation for sharing
- **Better-scroll**: Enhanced mobile scrolling
- **Crypto-js**: Client-side encryption utilities

## Troubleshooting

### Common Issues

1. **WeChat Integration**: Ensure proper domain configuration in WeChat backend
2. **Token Expiry**: Check automatic refresh mechanism in request.js
3. **Mobile Compatibility**: Test on actual devices, not just simulators
4. **Performance**: Monitor bundle size and loading times
5. **Encryption**: Verify AES key consistency between frontend and backend
