const {
    NODE_ENV
} = process.env;

export default config => {
    if (NODE_ENV === 'production') {
        config.merge({
            optimization: {
                minimize: true,
                splitChunks: {
                    cacheGroups: {
                        app: {
                            name: 'app',
                            test({ context }) {
                                return /react|core-js|moment/.test(context);
                            },
                            chunks: 'all',
                            minSize: 10,
                            minChunks: 1,
                            priority: 10
                        },
                        // 指定包单独打包
                        vendor: {
                            test({ context }) {
                                return /html2canvas|pdfjs-dist|react-file-viewer/.test(context);
                            },
                            name({ context }) {
                                const packageName = context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1]
                                return `vendor.${packageName}`
                            },
                            priority: 30
                        }
                    },
                },
            }
        });
    }
}